import { Request, Response, NextFunction } from 'express';
import { Config } from '../models/Config';
import { HttpStatusCode } from 'axios';
import { logger } from './logger';

export const getConfig = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  // 从请求头部获取语言
  const acceptLanguageHeader = req.headers['accept-language'];
  const lang = acceptLanguageHeader ? acceptLanguageHeader.split(',')[0].split('-')[0] : 'en'; // 默认为英文
  const appVersion = req.headers['app-version']

  try {
    const config = await Config.findOne({appVersion}).lean() || await Config.findOne({appVersion: '1.0'}).lean();
    if (config) {
      // 获取客户端版本信息用于版本检测
      const clientVersion = req.headers['app-version'] as string || '1.0.0';
      const platform = req.headers['platform'] as string || 'ios';

      // 版本比较逻辑
      const compareVersions = (v1: string, v2: string): number => {
        const v1Parts = v1.split('.').map(Number);
        const v2Parts = v2.split('.').map(Number);

        while (v1Parts.length < 3) v1Parts.push(0);
        while (v2Parts.length < 3) v2Parts.push(0);

        for (let i = 0; i < 3; i++) {
          if (v1Parts[i] > v2Parts[i]) return 1;
          if (v1Parts[i] < v2Parts[i]) return -1;
        }
        return 0;
      };

      // 检查版本更新状态
      const latestVersionComparison = compareVersions(config.latestVersion || '1.0.0', clientVersion);
      const minimumVersionComparison = compareVersions(clientVersion, config.minimumVersion || '1.0.0');

      let needUpdate = false;
      let updateType: 'force' | 'optional' | 'none' = 'none';

      if (minimumVersionComparison < 0) {
        needUpdate = true;
        updateType = 'force';
      } else if (latestVersionComparison > 0) {
        needUpdate = true;
        updateType = config.forceUpdate ? 'force' : 'optional';
      }

      const configData = {
        appVersion: config.appVersion,
        privacyPolicy: config.privacyPolicy,
        termsOfService: config.termsOfService,
        supportEmail: config.supportEmail,
        featureFlags: config.featureFlags,
        compressRate: config.compressRate,
        // @ts-ignore
        promotLocal: config.promotLocal && lang in config.promotLocal ? config.promotLocal[lang] : null,
        // @ts-ignore
        promotCloud: config.promotCloud && lang in config.promotCloud ? config.promotCloud[lang] : null,
        // @ts-ignore
        mainSolgan: config.mainSolgan[lang] || config.mainSolgan['en'],
        // @ts-ignore
        registerSolgan: config.registerSolgan[lang] || config.registerSolgan['en'],
        // @ts-ignore
        emailLoginSolgan: config.emailLoginSolgan[lang] || config.emailLoginSolgan['en'],
        // @ts-ignore
        rechargeMessages: config.rechargeMessages[lang] || config.rechargeMessages['en'],
        // @ts-ignore
        hideMessage: config.hideMessage[lang] || config.hideMessage['en'],
        // @ts-ignore
        rechargeDescription: config.rechargeDescription[lang] || config.rechargeDescription['en'],
        // 版本控制信息
        versionControl: {
          needUpdate,
          updateType,
          latestVersion: config.latestVersion || '1.0.0',
          minimumVersion: config.minimumVersion || '1.0.0',
          // @ts-ignore
          updateMessage: config.updateMessage?.get(lang) || config.updateMessage?.get('en') || '发现新版本，建议立即更新',
          downloadUrl: platform === 'android' ? config.appStoreUrls?.android : config.appStoreUrls?.ios,
          versionCheckEnabled: config.versionCheckEnabled !== false
        }
      };
      res.json({ code: HttpStatusCode.Ok, message: 'success', data: configData });
    } else {
      next({ code: HttpStatusCode.NotFound, message: req.t('data_not_found') });
    }
  } catch (error) {
    logger.debug('Error getting config:', error);
    next({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
  }
};

export const limit = async (req: Request, res: Response, next: NextFunction): Promise<void> => {

  // res.json({
  //   code: 10001,
  //   message: 'success',
  //   data: {
  //     msg: '',
  //     abValue: '',
  //     limit: false,
  //     subMsg: '～',
  //     extMsg: '',
  //     freeEquityStatus: false,
  //     type: 2,
  //     remainFreeCount: 0
  //   },
  //   success: true
  // });
  
}
